
SHELL:=/bin/bash

.DEFAULT_GOAL := help

.PHONY: help
help:
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) |  awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

CWD = $(shell pwd)
GIT_COMMIT = $(shell git rev-parse HEAD)
BRANCH_NAME = $(shell git symbolic-ref --short HEAD)

DEFAULT_STAGE = dev

### Variables for tagging
SERVICE = s2a-performance

RK_BUCKET ?= rk-deploy-jenkins-dev
VPC_STACK_NAME = performance-vpc
S3_PATH = $(SERVICE_NAME)/$(BRANCH_NAME)
CFN_LOCATION = $(CWD)/deployment/cloudformation

################################################################
######    Fall back to targets in shared Makefile     ##########
################################################################

# Hack to automatically update submodules
SUBMODULE := $(shell git submodule update --init --recursive)

.PHONY: %
%: Makefile
	@$(MAKE) -e -f .configs/Makefile $@

.PHONY: Makefile
Makefile: ;

#################################################
# Service specific config
SERVICE_NAME = performance-analytics-service
# functions (directory names)
LAMBDA_FKN_ERROR_LIST = error-list-service
LAMBDA_FKN_COMMENTS = comments-service
LAMBDA_DB_USER_CREATOR = custom-database-user-creator
LAMBDA_FKN_CUSTOMER_SETTINGS_SERVICE = customer-settings-service
LAMBDA_FKN_KPI_SERVICE = performance-analytics-kpi-service
LAMBDA_FKN_PERFORMANCE = performance-analytics-service
LAMBDA_CONFIG_GENERATOR = performance-analytics-service-config-generator
LAMBDA_FKN_UNITS_SERVICE = performance-analytics-units-service
LAMBDA_FKN_PRODUCTS_SPEEDS_SERVICE = products-speeds-service
LAMBDA_FKN_THRESHOLD = threshold-service
LAMBDA_FKN_CHECKMAT_CURVE = checkmat-curve-service
LAMBDA_FKN_UNITS_V2_SERVICE = performance-analytics-units-v2-service

LAMBDA_LIST = $(LAMBDA_FKN_KPI_SERVICE) \
			  $(LAMBDA_FKN_PERFORMANCE) \
			  $(LAMBDA_CONFIG_GENERATOR) \
			  $(LAMBDA_FKN_PRODUCTS_SPEEDS_SERVICE) \
			  $(LAMBDA_FKN_THRESHOLD) \
			  $(LAMBDA_FKN_CUSTOMER_SETTINGS_SERVICE) \
			  $(LAMBDA_FKN_COMMENTS) \
			  $(LAMBDA_DB_USER_CREATOR) \
			  $(LAMBDA_FKN_UNITS_SERVICE) \
				$(LAMBDA_FKN_CHECKMAT_CURVE) \
				$(LAMBDA_FKN_ERROR_LIST) \
				$(LAMBDA_FKN_UNITS_V2_SERVICE)

TEST_SERVICES := $(foreach service,$(LAMBDA_LIST), test-$(service))

PARAMETER_OVERRIDES = LambdaPerformanceAnalyticsServiceDeploy=$(S3_PATH)/$(LAMBDA_FKN_PERFORMANCE)-$(GIT_COMMIT).zip \
	LambdaThresholdDeploy=$(S3_PATH)/$(LAMBDA_FKN_THRESHOLD)-$(GIT_COMMIT).zip \
	LambdaKpiServiceDeploy=$(S3_PATH)/$(LAMBDA_FKN_KPI_SERVICE)-$(GIT_COMMIT).zip \
	LambdaKpiServicePublicAPIDeploy=$(S3_PATH)/$(LAMBDA_FKN_KPI_SERVICE)-$(GIT_COMMIT).zip \
	LambdaCustomerSettingsDeploy=$(S3_PATH)/$(LAMBDA_FKN_CUSTOMER_SETTINGS_SERVICE)-$(GIT_COMMIT).zip \
	LambdaProductsSpeedsDeploy=$(S3_PATH)/$(LAMBDA_FKN_PRODUCTS_SPEEDS_SERVICE)-$(GIT_COMMIT).zip \
	LambdaConfigGeneratorDeploy=$(S3_PATH)/$(LAMBDA_CONFIG_GENERATOR)-$(GIT_COMMIT).zip \
	LambdaCommentsDeploy=${S3_PATH}/${LAMBDA_FKN_COMMENTS}-$(GIT_COMMIT).zip \
	LambdaErrorListDeploy=${S3_PATH}/${LAMBDA_FKN_ERROR_LIST}-$(GIT_COMMIT).zip \
	LambdaUnitsReportV2ServiceDeploy=${S3_PATH}/${LAMBDA_FKN_UNITS_V2_SERVICE}-$(GIT_COMMIT).zip \
	LambdaCheckmatCurveDeploy=${S3_PATH}/${LAMBDA_FKN_CHECKMAT_CURVE}-$(GIT_COMMIT).zip \

#######################################################
# run centrally for whole build
define run_all
	for dir in $(LAMBDA_LIST); do \
	  echo; \
		echo; \
		echo "░░ Running for $$dir ░░"; \
		echo; \
		cd $$dir; make $1; cd ../; \
		echo; \
	done
endef

.PHONY: cfn-lint
cfn-lint: ## Lint the AWS CloudFormation template
	sed -i _backup "s/%%TIMESTAMP%%/$(shell date +%s)/g" $(CFN_LOCATION)/${SERVICE_NAME}.json

	docker-compose up -d ${SERVICE_NAME}
	docker-compose exec -T ${SERVICE_NAME} bash -c "pip3 install cfn-lint"

	@echo ""
	@echo "**************************"
	@echo "* Run cfn-lint in docker *"
	@echo "**************************"
	@echo ""
	docker-compose exec -T ${SERVICE_NAME} bash -c "cfn-lint -i E3686" || true
	@echo ""
	@echo ""

	docker-compose rm --stop --force ${SERVICE_NAME}

	cp $(CFN_LOCATION)/$(SERVICE_NAME).json_backup $(CFN_LOCATION)/${SERVICE_NAME}.json || true
	rm -f $(CFN_LOCATION)/${SERVICE_NAME}.json_backup

.PHONY: install
install: ## Install the dependencies of all services
	poetry config http-basic.syskron-jfrog $(PYPI_USER) $(PYPI_PASS)
	poetry install --no-root
	poetry run pre-commit install --install-hooks
	@$(call run_all,install)

.PHONY: init
init:
	poetry config virtualenvs.in-project true
	git submodule update --init --recursive
	@$(call run_all,init)

.PHONY: ci-init
ci-init:
	poetry config virtualenvs.in-project true
	@poetry config http-basic.syskron-jfrog $(PYPI_USER) $(PYPI_PASS)

.PHONY: lock
lock:
	@echo "███ Running poetry lock"
	poetry lock
	@echo
	@$(call run_all,lock)

#################################
#########    Format    ##########
#################################
.PHONY: format
format:
	@$(MAKE) -C ./performance-analytics-service-config-generator format

.PHONY: typecheck
typecheck:
	@$(MAKE) -C ./performance-analytics-service-config-generator typecheck

.PHONY: lint
lint: ## Lint all services
	@$(call run_all,lint)

.PHONY: test
test: ## Run tests for all services
	@$(call run_all,test)

################################
#########   Bundle   ###########
################################
.PHONY: bundle
bundle:
	@rm -rf ./bundle/
	@mkdir -p ./bundle/
	@$(call run_all,bundle; cp build/distributions/$$dir-$(GIT_COMMIT).zip ../bundle)

.PHONY: upload
upload: ## Upload all lambdas to S3 bucket
	aws s3 cp bundle s3://$(RK_BUCKET)/$(S3_PATH) --recursive

.PHONY: deploy
deploy: ## THIS COMMAND IS NOT USED IN JENKINS!
	cp $(CFN_LOCATION)/$(SERVICE_NAME).json $(CFN_LOCATION)/tmp_cf.json
	sed -i -e "s/%%TIMESTAMP%%/$(shell date +%s)/g" $(CFN_LOCATION)/tmp_cf.json
	aws cloudformation deploy \
		--template-file  $(CFN_LOCATION)/tmp_cf.json \
		--stack-name $(SERVICE_NAME) \
		--parameter-overrides $(PARAMETER_OVERRIDES) \
		--s3-bucket $(RK_BUCKET) \
		--capabilities CAPABILITY_NAMED_IAM \
		--no-fail-on-empty-changeset \
		--tags "SERVICE=$(SERVICE)"

.PHONY: deploy-vpc
deploy-vpc: ## Deploy AWS Cloudformation - VPC
	aws cloudformation deploy \
		--template-file deployment/cloudformation/performance-vpc.json \
		--stack-name $(VPC_STACK_NAME) \
		--s3-bucket $(RK_BUCKET) \
		--capabilities CAPABILITY_NAMED_IAM \
		--no-fail-on-empty-changeset\
		--tags "SERVICE=$(SERVICE)" \


.PHONY: deploy-rds
deploy-rds: ## Deploy AWS Cloudformation - RDS
	aws cloudformation deploy \
		--template-file deployment/cloudformation/performance-rds.json \
		--stack-name performance-rds \
		--parameter-overrides \
			PerformanceVPCStack=$(VPC_STACK_NAME) \
			DatabaseUserCreatorLambdaDeploy=$(S3_PATH)/$(LAMBDA_DB_USER_CREATOR)-$(subst /,-,$(BRANCH_NAME))-$(GIT_COMMIT).zip \
			Env=$(DEFAULT_STAGE) \
		--s3-bucket $(RK_BUCKET) \
		--capabilities CAPABILITY_NAMED_IAM \
		--no-fail-on-empty-changeset\
		--tags "SERVICE=$(SERVICE)" \


.PHONY: deploy-performance-dynamodb
deploy-performance-dynamodb: ## Deploy AWS Cloudformation - Performance DynamoDB
	aws cloudformation deploy \
		--template-file deployment/cloudformation/performance-dynamodb.json \
		--stack-name performance-dynamodb \
		--s3-bucket $(RK_BUCKET) \
		--capabilities CAPABILITY_NAMED_IAM \
		--no-fail-on-empty-changeset\
		--tags "SERVICE=$(SERVICE)"

.PHONY: clean
clean:
	rm -rf bundle/
	$(call run_all,clean)

.PHONY: docker-build
docker-build: ## Build all test-* services
ifdef service
	docker-compose build --force-rm --progress tty test-$(service)
else
	docker-compose build --force-rm --parallel --progress tty ${TEST_SERVICES}
endif

	@echo -e ${pycharm_warning_text}


.PHONY: docker-shell
docker-shell: ## Start interactive container test-<service_name> using make docker-shell service=<service_name>
	docker-compose run\
		--rm \
		test-$(service)

.PHONY: docker-shell-detached
docker-shell-detached: ## Start detachted container test-<service_name> using make docker-shell-detachted service=<service_name>
	docker-compose run\
		--rm \
		-d \
		test-$(service)

.PHONY: docker-test
docker-test: ## Run tests for all services
	for dir in  $(LAMBDA_LIST); do \
		docker-compose run \
			--rm \
			test-$$dir -c "make test"; \
	done

.PHONY: docker-clean
docker-clean: ## Clean up the docker environment for the test-* services
	# -v removes all created volumes
	docker rm -v --force \
		$$(docker ps -a --filter=name="${SERVICE_NAME}*" -q) &> /dev/null || true #ignore warnings and errors

	docker rmi --force \
		$$(docker images --filter=reference="${SERVICE_NAME}*" -q) &> /dev/null || true #ignore warnings and errors

	@echo -e ${docker_clean_warning_text}

# Warnings definitions
define docker_clean_warning_text
" \n \033[0;31m!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n\
	! The cleanup removed all containers and images associated \n\
	! to the ${SERVICE_NAME} repository.\n\
	! To remove further IDE or build artifacts use: \n\
	! \n\
	! \t docker container prune \n\
	! \t docker image prune \n\
	! \t docker builder prune \n\
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\033[0m\n"
endef

define pycharm_warning_text
" \n \033[0;31m!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n\
  ! Warning for PyCharm users:\n\
  ! \n\
  ! If you use make docker-build to update dependencies of a service\n\
  ! make sure to restart your IDE or reload the python interpreter\n\
  ! and corresponding external libraries. Otherwise you are faced\n\
	! with the old version in the IDE.\n\
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\033[0m\n"
endef
