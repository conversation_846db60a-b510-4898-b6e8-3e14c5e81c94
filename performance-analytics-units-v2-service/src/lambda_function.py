# Copyright (c) 2021, Syskron GmbH. All rights reserved.

import os

from aws_lambda_powertools import Tracer
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from lib_cloud_sdk.util.sentry.init_fastapi import init_sentry
from mangum import Mangum
from Secweb import SecWeb
from Secweb.CacheControl import CacheControl

from api.documentation.metadata import get_tags_metadata
from api.route_handler import router as api_router

TRACER = Tracer()

init_sentry()
app = FastAPI(
    version="1.0.0",
    title="Performance Analytics Counter Report Service API",
    description="API for Counter Report",
    openapi_tags=get_tags_metadata(),
)

app.include_router(api_router, prefix="/v1/performance-analytics")

CORS_ALLOW_ORIGIN = os.getenv("CORS_ALLOW_ORIGIN", "*")

app.add_middleware(
    CORSMiddleware,
    allow_origins=[CORS_ALLOW_ORIGIN],
    allow_methods=["*"],
    allow_headers=["Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token"],
)

SecWeb(app=app, Option={"corp": "same-site"})
app.add_middleware(CacheControl, Option={"no-cache": True})


lambda_handler = Mangum(app)
