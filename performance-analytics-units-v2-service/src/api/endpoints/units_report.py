from aws_lambda_powertools import Logger, Tracer
from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request, status
from lib_cloud_sdk.util.common import validate_fast_api_timerange
from performance_analytics.fast_api.dependencies.s2a_properties import Share2ActProperties

from api.api_schemas.models import CounterReportResponse
from api.endpoints.utils import get_block_counter_report, get_machine_counter_report

LOGGER = Logger()

router = APIRouter()

TRACER = Tracer()

DEFAULT_TIME_RANGE_MS = 8 * 60 * 60 * 1000


@router.get(
    "/v2/{line_id}",
    response_model=CounterReportResponse,
    response_model_exclude_none=True,
    tags=["counter-report"],
    operation_id="block_counter_report",
    dependencies=[Depends(validate_fast_api_timerange)],
)
@TRACER.capture_method(capture_response=False)
def block_counter_report(
    request: Request,
    properties: Share2ActProperties,
    line_id: str = Path(..., description="Line identifier"),
    time_from: int = Query(..., description="unix epoch timestamp in milliseconds"),
    time_to: int | None = Query(None, description="unix epoch timestamp in milliseconds "),
) -> CounterReportResponse:
    """Retrieve the block counter report for a specific line within a given time range.

    Args:
        request (Request): The FastAPI request object.
        properties (Share2ActProperties): Properties for the request.
        line_id (str): Line identifier (from path).
        time_from (int): Start of the time range (unix epoch in milliseconds, from query).
        time_to (Optional[int]): End of the time range (unix epoch in milliseconds, from query).

    Returns:
        CounterReportResponse: The block counter report response.

    Raises:
        HTTPException: If an error occurs during report retrieval.
    """
    LOGGER.debug(
        "block_counter_report() called with event: %s",
        request.scope.get("aws.event", {}),
    )
    if time_from and not time_to:
        time_to = time_from + DEFAULT_TIME_RANGE_MS
    if time_to is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="time_to must be provided or computable.",
        )
    LOGGER.info(
        "Processing line counter report request",
        extra={
            "line_id": line_id,
            "time_from": time_from,
            "time_to": time_to,
            "account": properties.account,
        },
    )

    try:
        account = properties.account
        if isinstance(account, list):
            account = account[0] if account else ""
        base_response = {
            "customer": account,
            "eq_id": line_id,
            "time_from": time_from,
            "time_to": time_to,
        }


        result = get_block_counter_report(
            base_response=base_response,
            line_id=line_id,
            account=account,
            time_from=time_from,
            time_to=time_to,
        )

        return result
    except HTTPException as http_excep:
        LOGGER.exception(
            http_excep,
            extra={
                "line_id": line_id,
                "account_id": properties.account,
            },
        )
        raise http_excep
    except Exception as excep:
        LOGGER.exception(
            excep,
            extra={
                "line_id": line_id,
                "account_id": properties.account,
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        ) from excep


@router.get(
    "/v2/{line_id}/{machine_id}",
    response_model=CounterReportResponse,
    response_model_exclude_none=True,
    tags=["counter-report"],
    operation_id="machine_counter_report",
    dependencies=[Depends(validate_fast_api_timerange)],
)
@TRACER.capture_method(capture_response=False)
def machine_counter_report(
    request: Request,
    properties: Share2ActProperties,
    line_id: str = Path(..., description="Line identifier"),
    machine_id: str = Path(..., description="Machine identifier"),
    time_from: int = Query(..., description="unix epoch timestamp in milliseconds"),
    time_to: int | None = Query(None, description="unix epoch timestamp in milliseconds"),
) -> CounterReportResponse:
    """Retrieve the machine counter report for a specific machine within a given time range.

    Args:
        request (Request): The FastAPI request object.
        properties (Share2ActProperties): Properties for the request.
        line_id (str): Line identifier (from path).
        machine_id (str): Machine identifier (from path).
        time_from (int): Start of the time range (unix epoch in milliseconds, from query).
        time_to (Optional[int]): End of the time range (unix epoch in milliseconds, from query).

    Returns:
        CounterReportResponse: The machine counter report response.

    Raises:
        HTTPException: If an error occurs during report retrieval.
    """
    LOGGER.debug(
        "machine_counter_report() called with event: %s",
        request.scope.get("aws.event", {}),
    )
    if time_from and not time_to:
        time_to = time_from + DEFAULT_TIME_RANGE_MS
    if time_to is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="time_to must be provided or computable.",
        )
    LOGGER.info(
        "Processing machine counter report request",
        extra={
            "line_id": line_id,
            "machine_id": machine_id,
            "time_from": time_from,
            "time_to": time_to,
            "account": properties.account,
        },
    )

    try:
        account = properties.account
        if isinstance(account, list):
            account = account[0] if account else ""
        base_response = {
            "customer": account,
            "eq_id": machine_id,
            "time_from": time_from,
            "time_to": time_to,
        }

        result = get_machine_counter_report(
            base_response=base_response,
            machine_id=machine_id,
            account=account,
            time_from=time_from,
            time_to=time_to,
        )

        return result
    except HTTPException as http_excep:
        LOGGER.exception(
            http_excep,
            extra={
                "line_id": line_id,
                "account_id": properties.account,
            },
        )
        raise http_excep
    except Exception as excep:
        LOGGER.exception(
            excep,
            extra={
                "line_id": line_id,
                "account_id": properties.account,
            },
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        ) from excep
