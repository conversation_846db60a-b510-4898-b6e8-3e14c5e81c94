# Copyright (c) 2021, Syskron GmbH. All rights reserved.


from http import HTTPStatus
import json
from unittest.mock import Mock, patch

from machine_data_query.models.speeds import (
    DESIGN_SPEEDS,
    SET_SPEEDS,
    SpeedCounterItem,
    SpeedItem,
    Speeds,
)
import pytest

SPEEDS_MOCK = Mock()
VERSION_MOCK = Mock()

# Mock S2A Properties for FastAPI Lambda tests
_request_context = {
    "authorizer": {
        "claims": json.dumps({"scopes": ["performance-analytics"]}),
        "principalId": json.dumps("some-principal-id"),
        "user": json.dumps({
            "userId": "test-user-id",
            "username": "test-user",
            "login": "test-login",
            "groups": ["group-1", "site-manager"]
        }),
        "account": json.dumps({"accountId": "test-account-id", "userPoolId": "some-user-pool"}),
    }
}


def _read_json_file(file_name: str) -> dict:
    with open(file_name) as file_obj:
        file_data = file_obj.read()
    return json.loads(file_data)


def _create_speeds_from_json_file(file_name: str) -> tuple[Speeds, Speeds, Speeds]:
    data = _read_json_file(file_name)
    machine = data["test-machine-id"]
    time_from = machine["time_from"]
    time_to = machine["time_to"]
    speeds_items = [
        SpeedCounterItem(
            start=item["start"],
            end=item["end"],
            current_speed=item["current_speed"],
            units_produced=item["units_produced"],
            units_defect=item["units_defect"],
            units_total=item.get("units_total"),
            station_counters=item.get("station_counters"),
        )
        for item in machine["speeds"]
    ]
    design_speeds_items = [
        SpeedItem(start=item["start"], end=item["end"], type=DESIGN_SPEEDS, speed=item["speed"])
        for item in machine["design_speeds"]
    ]
    set_speeds_items = [
        SpeedItem(start=item["start"], end=item["end"], type=SET_SPEEDS, speed=item["speed"])
        for item in machine["set_speeds"]
    ]
    speeds = Speeds(time_from=time_from, time_to=time_to, items=speeds_items)
    design_speeds = Speeds(time_from=time_from, time_to=time_to, items=design_speeds_items)
    set_speeds = Speeds(time_from=time_from, time_to=time_to, items=set_speeds_items)
    return (speeds, design_speeds, set_speeds)


def setup_module(module):
    module.patcher = patch.dict(
        "sys.modules",
        {
            "machine_data_query.query_speeds": SPEEDS_MOCK,
            "lib_cloud_sdk.version": VERSION_MOCK,
        },
    )
    module.patcher.start()


def teardown_module(module):
    module.patcher.stop()


@pytest.fixture(scope="session", autouse=True)
def s2a_properties_mock():
    from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties

    with patch("performance_analytics.fast_api.dependencies.s2a_properties.CommonShare2ActProperties") as event_mock:
        properties = CommonShare2ActProperties(_request_context)
        event_mock.return_value = properties
        yield event_mock


@pytest.fixture
def lambda_handler():
    """
    Unit under test - Lambda handler for FastAPI
    """
    from src.lambda_function import lambda_handler
    return lambda_handler


def _create_lambda_event(machine_id: str = "test-machine-id", line_id: str = "test-line-id",
                        time_from: str | None = None, time_to: str | None = None, line_kpi: int | None = None) -> dict:
    """Create a Lambda event for testing."""
    query_params = {}
    multi_value_query_params = {}

    if time_from:
        query_params["time_from"] = time_from
        multi_value_query_params["time_from"] = [time_from]
    if time_to:
        query_params["time_to"] = time_to
        multi_value_query_params["time_to"] = [time_to]
    if line_kpi is not None:
        query_params["line_kpi"] = str(line_kpi)
        multi_value_query_params["line_kpi"] = [str(line_kpi)]

    return {
        "resource": f"/v1/performance-analytics/units-report/v2/{line_id}/{machine_id}",
        "path": f"/v1/performance-analytics/units-report/v2/{line_id}/{machine_id}",
        "httpMethod": "GET",
        "headers": {
            "accept": "application/json",
            "content-type": "application/json",
        },
        "multiValueHeaders": {
            "accept": ["application/json"],
            "content-type": ["application/json"],
        },
        "queryStringParameters": query_params if query_params else None,
        "multiValueQueryStringParameters": multi_value_query_params if multi_value_query_params else None,
        "pathParameters": {
            "line_id": line_id,
            "machine_id": machine_id
        },
        "stageVariables": None,
        "requestContext": {
            "resourceId": "abc123",
            "resourcePath": f"/v1/performance-analytics/units-report/v2/{line_id}/{machine_id}",
            "httpMethod": "GET",
            "extendedRequestId": "req-123",
            "requestTime": "01/Jan/2021:00:00:00 +0000",
            "path": f"/test/v1/performance-analytics/units-report/v2/{line_id}/{machine_id}",
            "accountId": "test-account-id",
            "protocol": "HTTP/1.1",
            "stage": "test",
            "domainPrefix": "test-domain",
            "requestTimeEpoch": *************,
            "requestId": "test-request-id",
            "identity": {
                "sourceIp": "127.0.0.1",
                "userAgent": "test-agent"
            },
            "domainName": "test-domain.com",
            "apiId": "test-api-id",
            "authorizer": {
                "claims": '{"scopes": ["performance-analytics"]}',
                "user": '{"userId": "test-user-id", "username": "test-user", "login": "test-login", "groups": []}',
                "account": '{"accountId": "test-account-id"}',
            }
        },
        "body": None,
        "isBase64Encoded": False
    }


@pytest.fixture
def mock_query_counters():
    with patch("api.endpoints.utils.query_counters") as mock:
        mock.return_value = {
            "units_produced": 33.0,
            "units_defect": 0.0,
            "units_total": 33.0,
            "station_counters": {
                "SYS_STAT_CONSTRAINT_DATA_KEY_1_Delta": 13,
                "SYS_STAT_CONSTRAINT_DATA_KEY_2_Delta": 20,
                "SYS_STAT_CONSTRAINT_DATA_KEY_3_Delta": 14,
            }
        }
        yield mock


def _calculate_totals_from_speeds_data(file_name: str) -> dict:
    """Calculate total units from speeds data in JSON file."""
    speeds_data, _, _ = _create_speeds_from_json_file(file_name)

    total_units_produced = sum(item.units_produced for item in speeds_data.items)
    total_units_defect = sum(item.units_defect for item in speeds_data.items)
    total_units_total = sum(item.units_total or 0 for item in speeds_data.items)

    # Aggregate station counters
    station_counters = {}
    for item in speeds_data.items:
        if item.station_counters:
            for key, value in item.station_counters.items():
                station_counters[key] = station_counters.get(key, 0) + value

    result = {
        "units_produced": total_units_produced,
        "units_defect": total_units_defect,
        "units_total": total_units_total,
    }

    if station_counters:
        result["station_counters"] = station_counters

    return result


@pytest.fixture
def mock_query_counters_with_station_counters():
    """Mock query_counters with data from dynamo_db_machine_data_with_station_counters.json"""
    with patch("api.endpoints.utils.query_counters") as mock:
        mock.return_value = _calculate_totals_from_speeds_data(
            "test/res/dynamo_db_machine_data_with_station_counters.json"
        )
        yield mock


@pytest.fixture
def mock_query_counters_with_station_counters_2():
    """
    Mock query_counters with data from dynamo_db_machine_data_with_station_counters_2.json
    (units not matching fully)
    """
    with patch("api.endpoints.utils.query_counters") as mock:
        mock.return_value = _calculate_totals_from_speeds_data(
            "test/res/dynamo_db_machine_data_with_station_counters_2.json"
        )
        yield mock


@pytest.fixture
def mock_query_counters_basic():
    """Mock query_counters with data from dynamo_db_machine_data.json"""
    with patch("api.endpoints.utils.query_counters") as mock:
        mock.return_value = _calculate_totals_from_speeds_data(
            "test/res/dynamo_db_machine_data.json"
        )
        yield mock


@pytest.fixture
def mock_query_counters_wo_totals():
    """Mock query_counters with data from dynamo_db_machine_data_wo_totals.json"""
    with patch("api.endpoints.utils.query_counters") as mock:
        mock.return_value = _calculate_totals_from_speeds_data(
            "test/res/dynamo_db_machine_data_wo_totals.json"
        )
        yield mock


@pytest.fixture(autouse=True)
def version():
    VERSION_MOCK.reset_mock()
    VERSION_MOCK.get_version.return_value = "test-version"
    return VERSION_MOCK


def test_get_units_report(lambda_handler, mock_query_counters):
    """Test getting units report with station counters."""
    event = _create_lambda_event(
        time_from="*************",
        time_to="*************"
    )

    actual_result = lambda_handler(event, "context")
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.OK
    # Check camelCase response format (FastAPI with alias_generator=to_camel)
    assert body == {
        "customer": "test-account-id",
        "eqId": "test-machine-id",
        "timeFrom": *************,
        "timeTo": *************,
        "unitsProduced": 33,
        "unitsDefect": 0,
        "unitsTotal": 33,
        "stationCounters": {
            "SYS_STAT_CONSTRAINT_DATA_KEY_1_Delta": 13,
            "SYS_STAT_CONSTRAINT_DATA_KEY_2_Delta": 20,
            "SYS_STAT_CONSTRAINT_DATA_KEY_3_Delta": 14,
        },
    }


def test_get_units_report_missing_time_from(lambda_handler):
    """Test error handling when time_from parameter is missing."""
    event = _create_lambda_event(
        time_to="*************"
        # time_from is missing
    )

    actual_result = lambda_handler(event, "context")

    # The validation dependency returns 400 for missing required parameters
    assert actual_result["statusCode"] == HTTPStatus.BAD_REQUEST

    # Check if response body is JSON or plain text
    try:
        body = json.loads(actual_result["body"])
        assert "detail" in body or "message" in body
    except json.JSONDecodeError:
        # If it's plain text, just check it's not empty
        assert actual_result["body"]


def test_get_units_report_invalid_time_from(lambda_handler):
    """Test error handling when time_from parameter is invalid."""
    event = _create_lambda_event(
        time_from="invalid",
        time_to="*************"
    )

    actual_result = lambda_handler(event, "context")

    # The validation dependency causes a 500 error for invalid timestamps
    assert actual_result["statusCode"] == HTTPStatus.INTERNAL_SERVER_ERROR

    # Check if response body is JSON or plain text
    try:
        body = json.loads(actual_result["body"])
        assert "detail" in body or "message" in body
    except json.JSONDecodeError:
        # If it's plain text (like "Internal Server Error"), just check it's not empty
        assert actual_result["body"]


def test_get_units_report_no_speeds_data(lambda_handler):
    """Test handling when no speeds data is found."""
    with patch("api.endpoints.utils.query_counters") as mock_query:
        # Simulate HTTPException being raised (no data found)
        from fastapi import HTTPException
        mock_query.side_effect = HTTPException(status_code=404, detail="No data found")

        event = _create_lambda_event(
            time_from="*************",
            time_to="*************"
        )

        actual_result = lambda_handler(event, "context")
        body = json.loads(actual_result["body"])

        assert actual_result["statusCode"] == HTTPStatus.OK
        # When query_counters fails, utils.py sets default values
        assert body == {
            "customer": "test-account-id",
            "eqId": "test-machine-id",
            "timeFrom": *************,
            "timeTo": *************,
            "unitsProduced": 0,
            "unitsDefect": 0,
            "unitsTotal": 0,
        }





def test_get_units_report_not_matching_units(lambda_handler):
    """Test getting units report with non-matching units."""
    with patch("api.endpoints.utils.query_counters") as mock_query:
        mock_query.return_value = {
            "units_produced": 33.0,
            "units_defect": 5.0,
            "units_total": 35.0,
            "station_counters": {
                "SYS_STAT_CONSTRAINT_DATA_KEY_1_Delta": 13,
                "SYS_STAT_CONSTRAINT_DATA_KEY_2_Delta": 20,
                "SYS_STAT_CONSTRAINT_DATA_KEY_3_Delta": 14,
            }
        }

        event = _create_lambda_event(
            time_from="*************",
            time_to="*************"
        )

        actual_result = lambda_handler(event, "context")
        body = json.loads(actual_result["body"])

        assert actual_result["statusCode"] == HTTPStatus.OK
        assert body == {
            "customer": "test-account-id",
            "eqId": "test-machine-id",
            "timeFrom": *************,
            "timeTo": *************,
            "unitsProduced": 33,
            "unitsDefect": 5,
            "unitsTotal": 35,
            "stationCounters": {
                "SYS_STAT_CONSTRAINT_DATA_KEY_1_Delta": 13,
                "SYS_STAT_CONSTRAINT_DATA_KEY_2_Delta": 20,
                "SYS_STAT_CONSTRAINT_DATA_KEY_3_Delta": 14,
            },
        }


def test_get_units_report_missing_time_to(lambda_handler, mock_query_counters):
    """Test error handling when time_to parameter is missing."""
    event = _create_lambda_event(
        time_from="*************"
        # time_to is missing - should default to time_from + 8 hours
    )

    actual_result = lambda_handler(event, "context")
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.OK
    assert "timeFrom" in body
    assert "timeTo" in body
    assert body["timeFrom"] == *************
    # Should be time_from + 8 hours (8 * 60 * 60 * 1000 = 28800000)
    assert body["timeTo"] == ************* + 28800000


def test_get_units_report_overlapping_timerange(lambda_handler, mocker):
    """Test getting units report with overlapping timerange status."""
    from performance_analytics.models.block_configuration import BlockCounterStatus

    # Mock the block level counters function
    mock_block_config = mocker.patch("api.endpoints.utils.get_block_level_counters")
    mock_block_config.return_value = mocker.Mock(
        status=BlockCounterStatus.OVERLAPPING_TIMERANGE_FOR_BLOCK_COUNTER,
        units_map={"units_produced": 100, "units_defect": 5, "units_total": 105},
        block_valid_from=*************,
    )

    # Mock query_counters to return the expected data
    mock_query = mocker.patch("api.endpoints.utils.query_counters")
    mock_query.return_value = {
        "units_produced": 100.0,
        "units_defect": 5.0,
        "units_total": 105.0,
    }

    # Mock equipment client
    mock_equipment = mocker.patch("api.endpoints.utils.EquipmentClient")
    mock_equipment.return_value.get_lead_machine_by_line_id.return_value = "test-machine-id"

    # Create event for line endpoint (no machine_id in path)
    event = _create_lambda_event(
        line_id="test-line-id",
        machine_id="",  # This will be ignored for line endpoint
        time_from="*************",
        time_to="*************"
    )
    # Modify the event to target the line endpoint
    event["resource"] = "/v1/performance-analytics/units-report/v2/{line_id}"
    event["path"] = "/v1/performance-analytics/units-report/v2/test-line-id"
    event["requestContext"]["resourcePath"] = "/v1/performance-analytics/units-report/v2/{line_id}"
    event["pathParameters"] = {"line_id": "test-line-id"}

    actual_result = lambda_handler(event, "context")
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.OK
    assert body["timeFrom"] == *************


def test_get_units_report_failed_status(lambda_handler, mocker):
    """Test getting units report with failed status."""
    from performance_analytics.models.block_configuration import BlockCounterStatus

    # Mock the block level counters function to return failed status
    mock_block_config = mocker.patch("api.endpoints.utils.get_block_level_counters")
    mock_block_config.return_value = mocker.Mock(
        status=BlockCounterStatus.INVALID_BLOCK_CONFIG,
        units_map={}
    )

    # Create event for line endpoint
    event = _create_lambda_event(
        line_id="test-line-id",
        machine_id="",
        time_from="*************",
        time_to="*************"
    )
    # Modify the event to target the line endpoint
    event["resource"] = "/v1/performance-analytics/counter-report/v2/{line_id}"
    event["path"] = "/v1/performance-analytics/counter-report/v2/test-line-id"
    event["requestContext"]["resourcePath"] = "/v1/performance-analytics/counter-report/v2/{line_id}"
    event["pathParameters"] = {"line_id": "test-line-id"}

    actual_result = lambda_handler(event, "context")

    # Should return 404 for invalid block config
    assert actual_result["statusCode"] == HTTPStatus.NOT_FOUND


def test_get_units_report_with_station_counters_none(lambda_handler):
    """Test getting units report when station counters are None."""
    with patch("api.endpoints.utils.query_counters") as mock_query:
        # Return data without station_counters
        mock_query.return_value = {
            "units_produced": 33.0,
            "units_defect": 0.0,
            "units_total": 33.0,
            # No station_counters key
        }

        event = _create_lambda_event(
            time_from="*************",
            time_to="*************"
        )

        actual_result = lambda_handler(event, "context")
        body = json.loads(actual_result["body"])

        assert actual_result["statusCode"] == HTTPStatus.OK
        assert "stationCounters" not in body


def test_get_units_report_from_json_data(lambda_handler, mock_query_counters_with_station_counters):
    """Test getting units report with station counters using JSON test data."""
    event = _create_lambda_event(
        time_from="*************",
        time_to="*************"
    )

    actual_result = lambda_handler(event, "context")
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.OK
    # Check camelCase response format (FastAPI with alias_generator=to_camel)
    assert body == {
        "customer": "test-account-id",
        "eqId": "test-machine-id",
        "timeFrom": *************,
        "timeTo": *************,
        "unitsProduced": 33,
        "unitsDefect": 0,
        "unitsTotal": 33,
        "stationCounters": {
            "SYS_STAT_CONSTRAINT_DATA_KEY_1_Delta": 13,
            "SYS_STAT_CONSTRAINT_DATA_KEY_2_Delta": 20,
            "SYS_STAT_CONSTRAINT_DATA_KEY_3_Delta": 14,
        },
    }


def test_get_units_report_units_not_matching_from_json(lambda_handler, mock_query_counters_with_station_counters_2):
    """Test getting units report with non-matching units using JSON test data."""
    event = _create_lambda_event(
        time_from="*************",
        time_to="*************"
    )

    actual_result = lambda_handler(event, "context")
    body = json.loads(actual_result["body"])

    assert actual_result["statusCode"] == HTTPStatus.OK
    # The exact values will depend on what's in the JSON file
    assert "unitsProduced" in body
    assert "unitsDefect" in body
    assert "unitsTotal" in body
    assert "stationCounters" in body
