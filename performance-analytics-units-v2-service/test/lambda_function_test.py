import json
from unittest.mock import patch

import pytest

# Mock S2A Properties
_request_context = {
    "authorizer": {
        "claims": json.dumps({"scopes": ["performance-analytics"]}),
        "principalId": json.dumps("some-principal-id"),
        "user": json.dumps({
            "userId": "test-user-id",
            "username": "test-user",
            "login": "test-login",
            "groups": ["group-1", "site-manager"]
        }),
        "account": json.dumps({"accountId": "test-account-id", "userPoolId": "some-user-pool"}),
    }
}


@pytest.fixture(scope="session", autouse=True)
def s2a_properties_mock():
    from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties

    with patch("performance_analytics.fast_api.dependencies.s2a_properties.CommonShare2ActProperties") as event_mock:
        properties = CommonShare2ActProperties(_request_context)
        event_mock.return_value = properties
        yield event_mock


@pytest.fixture
def mock_query_counters():
    with patch("api.endpoints.utils.query_counters") as mock:
        mock.return_value = {
            "units_produced": 100.5,
            "units_defect": 5.2,
            "units_total": 105.7,
        }
        yield mock


@pytest.fixture
def counter_report_event():
    def _counter_report_event(request):
        time_from = request[0]
        time_to = request[1]
        has_times = bool(time_from and time_to)

        return {
            "resource": "/v1/performance-analytics/units-report/v2/{line_id}/{machine_id}",
            "path": "/v1/performance-analytics/units-report/v2/test-line-id/test-machine-id",
            "httpMethod": "GET",
            "headers": {
                "accept": "application/json",
                "content-type": "application/json",
            },
            "multiValueHeaders": {
                "accept": ["application/json"],
                "content-type": ["application/json"],
            },
            "queryStringParameters": {
                "time_from": time_from,
                "time_to": time_to
            } if has_times else None,
            "multiValueQueryStringParameters": {
                "time_from": [time_from],
                "time_to": [time_to]
            } if has_times else None,
            "pathParameters": {
                "line_id": "test-line-id",
                "machine_id": "test-machine-id"
            },
            "stageVariables": None,
            "requestContext": {
                "resourceId": "abc123",
                "resourcePath": "/v1/performance-analytics/units-report/v2/{line_id}/{machine_id}",
                "httpMethod": "GET",
                "extendedRequestId": "req-123",
                "requestTime": "01/Jan/2021:00:00:00 +0000",
                "path": "/test/v1/performance-analytics/units-report/v2/test-line-id/test-machine-id",
                "accountId": "test-account-id",
                "protocol": "HTTP/1.1",
                "stage": "test",
                "domainPrefix": "test-domain",
                "requestTimeEpoch": *************,
                "requestId": "test-request-id",
                "identity": {
                    "sourceIp": "127.0.0.1",
                    "userAgent": "test-agent"
                },
                "domainName": "test-domain.com",
                "apiId": "test-api-id",
                "authorizer": {
                    "claims": '{"scopes": ["performance-analytics"]}',
                    "user": '{"userId": "test-user-id", "username": "test-user", "login": "test-login", "groups": []}',
                    "account": '{"accountId": "test-account-id"}',
                }
            },
            "body": None,
            "isBase64Encoded": False
        }

    return _counter_report_event



@pytest.fixture
def lambda_handler():
    """
    Unit under test
    """
    from src.lambda_function import lambda_handler

    return lambda_handler


def test_lambda_handler_call_counter_report(
    lambda_handler, counter_report_event, mock_query_counters
):
    """Test whether counter report is called and returns the expected response."""
    report_event = counter_report_event(["*************", "*************"])
    actual_result = lambda_handler(report_event, "context")

    # Check that the response has the expected structure
    assert actual_result["statusCode"] == 200

    # Parse the response body
    response_body = json.loads(actual_result["body"])

    # Check the expected fields in the response (using camelCase as per the model)
    assert response_body["customer"] == "test-account-id"
    assert response_body["eqId"] == "test-machine-id"
    assert response_body["timeFrom"] == *************
    assert response_body["timeTo"] == *************
    assert response_body["unitsProduced"] == 100  # rounded from 100.5 (banker's rounding)
    assert response_body["unitsDefect"] == 5      # rounded from 5.2
    assert response_body["unitsTotal"] == 106     # rounded from 105.7

    # Verify that query_counters was called with the correct parameters
    mock_query_counters.assert_called_once_with(
        "test-account-id",  # account
        "test-machine-id",  # machine_id
        *************,      # time_from
        *************       # time_to
    )


def test_lambda_handler_call_wrong_query_param(
    lambda_handler, counter_report_event
):
    report_event = counter_report_event([None, None])
    actual_result = lambda_handler(report_event, "context")

    # Check that we get a 422 status code for missing query parameters
    assert actual_result["statusCode"] == 422

    # Parse the response body to check the error details
    response_body = json.loads(actual_result["body"])
    assert "detail" in response_body


@pytest.fixture
def wrong_args():
    return {
        "resource": "/v1/performance-analytics/units-report/v2/{line_id}/{machine_id}",
        "path": "/v1/performance-analytics/units-report/v2/test-line-id/test-machine-id",
        "httpMethod": "GET",
        "headers": {
            "accept": "application/json",
            "content-type": "application/json",
        },
        "multiValueHeaders": {
            "accept": ["application/json"],
            "content-type": ["application/json"],
        },
        "queryStringParameters": {
            "time_from": "123123abc"
        },
        "multiValueQueryStringParameters": {
            "time_from": ["123123abc"]
        },
        "pathParameters": {
            "line_id": "test-line-id",
            "machine_id": "test-machine-id"
        },
        "stageVariables": None,
        "requestContext": {
            "resourceId": "abc123",
            "resourcePath": "/v1/performance-analytics/counter-report/v2/{line_id}/{machine_id}",
            "httpMethod": "GET",
            "extendedRequestId": "req-123",
            "requestTime": "01/Jan/2021:00:00:00 +0000",
            "path": "/test/v1/performance-analytics/counter-report/v2/test-line-id/test-machine-id",
            "accountId": "test-account-id",
            "protocol": "HTTP/1.1",
            "stage": "test",
            "domainPrefix": "test-domain",
            "requestTimeEpoch": *************,
            "requestId": "test-request-id",
            "identity": {
                "sourceIp": "127.0.0.1",
                "userAgent": "test-agent"
            },
            "domainName": "test-domain.com",
            "apiId": "test-api-id",
            "authorizer": {
                "claims": '{"scopes": ["performance-analytics"]}',
                "user": '{"userId": "test-user-id", "username": "test-user", "login": "test-login", "groups": []}',
                "account": '{"accountId": "test-account-id"}',
            }
        },
        "body": None,
        "isBase64Encoded": False
    }



def test_catch_invalid_query_parameter(lambda_handler, wrong_args):
    from http import HTTPStatus

    actual_result = lambda_handler(wrong_args, "context")

    # FastAPI returns 422 for validation errors, not 400
    assert (
        actual_result["statusCode"] == HTTPStatus.UNPROCESSABLE_ENTITY
    ), 'Invalid timestamp for "time_from"'
